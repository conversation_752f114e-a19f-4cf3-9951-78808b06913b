## 3.1 问题建模与系统架构

### 3.1.1 分层决策架构设计

本文提出分层决策架构，将复杂的任务调度问题分解为两个相互独立但协调的子问题：

**强化学习层：任务卸载决策**

强化学习智能体专注于解决任务卸载决策问题，其动作空间为卸载策略向量：

$$\mathbf{D}_j(t) = (d_j^1, d_j^2, \ldots, d_j^{|\mathcal{Q}_j|}) \quad (3)$$

其中，每个元素$d_j^k$的取值来自所有可能的任务分配目标集合$\mathcal{M}_j(t)$：

(1) 本地处理：任务在本卫星上处理；

(2) 邻居卸载：卸载到邻居卫星$i$；

(3) 地面卸载：卸载到地面站；

(4) 任务丢弃：主动丢弃任务。

**算法层：本地资源分配**

对于被决策为"本地处理"的任务集合$\mathcal{Q}_j^{local}(t)$，系统采用DURA算法进行计算资源的智能分配，无需强化学习参与。

该分层架构的优势在于：(1) 专业化分工，强化学习专注于复杂的多智能体协调和卸载决策；(2) 降低复杂度，避免了强化学习处理高维连续动作空间的困难；(3) 提升效率，DURA算法的确定性计算比强化学习的随机探索更适合实时资源分配。


### 3.1.2 分布式部分可观测马尔可夫决策过程建模

LEO卫星星座的动态任务调度问题本质上是一个多智能体在不完全信息条件下的协同决策问题。本文将其严格建模为分布式部分可观测马尔可夫决策过程（Dec-POMDP），定义为六元组：

$$\mathcal{M} = \langle \mathcal{S}, \{\mathcal{A}_j\}_{j \in \mathcal{L}}, \mathcal{P}, \{\mathcal{R}_j\}_{j \in \mathcal{L}}, \{\Omega_j\}_{j \in \mathcal{L}}, \mathcal{O}, \gamma \rangle \quad (1)$$

其中$\mathcal{L}$表示LEO卫星智能体集合。

**状态空间$\mathcal{S}$：** 状态空间$\mathcal{S}$定义了系统的全局状态，包含所有LEO卫星的物理状态（轨道位置$\mathbf{p}_j(t)$、剩余能量$e_j(t)$）、计算状态（CPU利用率$\rho_j^{cpu}(t)$、任务队列$\mathcal{Q}_j(t)$）以及星间链路质量矩阵$\mathbf{L}(t)$：

$$s(t) = \{\{\mathbf{p}_j(t), e_j(t), \rho_j^{cpu}(t), \mathcal{Q}_j(t)\}_{j \in \mathcal{L}}, \mathbf{L}(t)\} \quad (2)$$

 观测空间

观测空间$\{\Omega_j\}_{j \in \mathcal{L}}$反映了LEO卫星的局部感知能力。由于通信和计算约束，智能体$j$只能获得局部观测：

$$o_j(t) = \{s_j^{local}(t), \mathcal{N}_j(t), \mathcal{T}_j^{user}(t)\} \quad (4)$$

其中$s_j^{local}(t)$是自身状态，$\mathcal{N}_j(t)$是可通信邻居状态，$\mathcal{T}_j^{user}(t)$是覆盖区域内的用户任务请求。

### 3.1.4 奖励函数设计

奖励函数$\{\mathcal{R}_j\}_{j \in \mathcal{L}}$设计为多目标优化，平衡任务完成率、能耗、延迟、负载均衡。本文采用分层奖励机制，包含个体奖励和全局奖励。

**个体奖励：** 考虑到任务的优先级差异，定义三个优先级等级：高优先级（Priority 1）、中优先级（Priority 2）、低优先级（Priority 3）。个体奖励函数为：

$$r_j^{local}(s, a) = \sum_{p=1}^{3} \alpha_p \cdot R_j^{comp,p} - \beta \cdot E_j^{cons} - \zeta \cdot D_j^{delay} \quad (5)$$

其中：$R_j^{comp,p}$表示智能体$j$完成的优先级$p$任务数量；$\alpha_p$表示优先级$p$任务的完成奖励权重，满足$\alpha_1 > \alpha_2 > \alpha_3$；$E_j^{cons}$表示能耗；$D_j^{delay}$表示平均延迟。

具体地，优先级权重设置为：$\alpha_1 = 10.0$，$\alpha_2 = 6.0$，$\alpha_3 = 3.0$。这种设计确保高优先级任务的完成能够获得更高的奖励，激励智能体优先处理重要任务。

**全局奖励：** 全局奖励函数为：

$$r_j^{regional}(s, a) = -\delta \cdot B_k(t) \quad (6)$$

其中$B_k(t)$表示区域$k$的负载方差，激励卫星做出有利于区域整体负载均衡的决策。

总奖励函数为：

$$r_j(s, a) = r_j^{local}(s, a) + r_j^{regional}(s, a) \quad (7)$$

其中$\alpha, \beta, \zeta, \delta$是权重参数，根据应用需求调节。

## 3.2 序列生成的MAPPO-Transformer卸载决策算法

强化学习层采用基于Transformer的MAPPO算法，专门处理任务卸载决策。该算法通过序列生成的方式，为任务队列中的每个任务生成卸载目标，实现高效的多智能体协同卸载。

## 3.3 基于动态效用感知的自适应资源分配算法

### 3.3.1 DURA算法概述

DURA（Dynamic Utility-aware Resource Allocation）算法作为分层架构的第二层，专门负责处理本地资源分配问题。当强化学习智能体完成卸载决策后，DURA算法接管所有被标记为"本地处理"的任务，通过动态效用评估实现计算资源的自适应分配。

DURA算法将传统的串行任务选择问题转化为并行资源分配问题，实现了从"选择处理哪个任务"到"同时为多个任务分配资源"的范式转变。

### 3.3.2 动态效用函数设计

任务$T_i$在时刻$t_{now}$的动态效用函数$U(T_i, t_{now})$由三个加权分量构成：

$$U(T_i, t_{now}) = w_p \cdot f_p(P_i) + w_d \cdot f_d(D_i, t_{now}) + w_{fail} \cdot f_{fail}(W_i) \quad (8)$$

其中静态价值因子$f_p(P_i) = P_i$反映任务的固有重要性。动态紧迫性因子采用对数形式：

$$f_d(D_i, t_{now}) = \log\left(1 + \frac{T_{remain, max}}{D_i - t_{now} + \epsilon}\right) \quad (9)$$

该对数函数相比传统的倒数函数具有更好的数值稳定性，其增长曲线符合边际效用递减原理。$T_{remain, max}$作为归一化常数确保不同时间尺度下的可比性，$\epsilon$防止数值异常。失败惩罚因子$f_{fail}(W_i) = W_i$量化任务失败造成的损失，使效用函数的各分量具有统一的正向贡献特性。

### 3.3.3 基于效用的资源分配机制

卫星MEC在任意时刻$t$维护活跃任务集$Q_{active}$，总计算资源$F_{total}$根据各任务的动态效用值进行比例分配：

$$F_i(t) = F_{total} \cdot \frac{U(T_i, t)}{\sum_{j \in Q_{active}} U(T_j, t)} \quad (10)$$

该分配机制形成自适应反馈闭环：任务接近截止时间时，其效用值$U_i$增加，自动获得更多计算资源$F_i$，加速处理进程；非紧急任务则维持最小资源分配，避免占用关键资源。这种内生的资源调节机制无需外部干预即可实现动态优化。

### 3.3.4 两阶段队列与准入控制

系统采用两级队列架构：等待队列$Q_{wait}$和活跃处理队列$Q_{active}$。新到达任务首先进入$Q_{wait}$，调度器周期性评估其可行性。任务$T_i$的准入条件基于最差情况可行性检查：

$$t_{now} + \frac{C_i}{F_{min\_share}} \leq D_i \quad (11)$$

其中$C_i$为任务计算复杂度，$F_{min\_share}$为基于历史数据或理论界限确定的最小资源保证。满足可行性条件的任务进入$Q_{active}$参与资源分配，不满足条件的任务根据系统策略继续等待或直接拒绝。该准入控制机制从源头避免将资源浪费在注定无法完成的任务上，提高系统整体效率。

## 3.4 分层协同调度流程

整个系统的调度流程体现了强化学习卸载决策与DURA资源分配的紧密协同，具体流程如下：

**步骤1：任务到达与队列管理**

新任务到达时被赋予参数$(P_i, D_i, W_i, C_i)$并加入任务队列$\mathcal{Q}_j$。

**步骤2：强化学习卸载决策**

LEO智能体基于当前观测生成卸载策略$\mathbf{D}_j$，为每个任务指定处理位置（本地、邻居、地面站或丢弃）。

**步骤3：本地任务筛选**

根据卸载决策，提取所有标记为"本地处理"的任务，形成本地任务集$\mathcal{Q}_j^{local}$。

**步骤4：DURA资源分配**

对于本地任务集，DURA算法执行以下步骤：

(1) 将本地任务分为等待队列$Q_{wait}$和活跃队列$Q_{active}$；

(2) 基于可行性检查将合格任务从$Q_{wait}$移入$Q_{active}$；

(3) 实时计算活跃任务的动态效用值并据此分配计算资源；

(4) 任务完成后释放资源，在下一周期重新分配。

**步骤5：反馈与学习**

DURA的资源分配结果作为环境反馈影响强化学习的奖励函数，形成闭环优化。

该分层协同机制确保了卸载决策与资源分配的最优匹配，实现了系统整体性能的最大化。

