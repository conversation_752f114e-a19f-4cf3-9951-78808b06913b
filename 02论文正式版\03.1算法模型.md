## 3.1 问题建模与系统架构

### 3.1.1 分层决策架构设计

本文提出分层决策架构，将复杂的任务调度问题分解为两个相互独立但协调的子问题：

**强化学习层：任务卸载决策**

强化学习智能体专注于解决任务卸载决策问题，其动作空间为卸载策略向量：

$$\mathbf{D}_j(t) = (d_j^1, d_j^2, \ldots, d_j^{|\mathcal{Q}_j|}) \quad (3)$$

其中，每个元素$d_j^k$的取值来自所有可能的任务分配目标集合$\mathcal{M}_j(t)$：

(1) 本地处理：任务在本卫星上处理；

(2) 邻居卸载：卸载到邻居卫星$i$；

(3) 地面卸载：卸载到地面站；

(4) 任务丢弃：主动丢弃任务。

**算法层：本地资源分配**

对于被决策为"本地处理"的任务集合$\mathcal{Q}_j^{local}(t)$，系统采用DURA算法进行计算资源的智能分配，无需强化学习参与。

该分层架构的优势在于：(1) 专业化分工，强化学习专注于复杂的多智能体协调和卸载决策；(2) 降低复杂度，避免了强化学习处理高维连续动作空间的困难；(3) 提升效率，DURA算法的确定性计算比强化学习的随机探索更适合实时资源分配。


### 3.1.2 分布式部分可观测马尔可夫决策过程建模

LEO卫星星座的动态任务调度问题本质上是一个多智能体在不完全信息条件下的协同决策问题。本文将其严格建模为分布式部分可观测马尔可夫决策过程（Dec-POMDP），定义为六元组：

$$\mathcal{M} = \langle \mathcal{S}, \{\mathcal{A}_j\}_{j \in \mathcal{L}}, \mathcal{P}, \{\mathcal{R}_j\}_{j \in \mathcal{L}}, \{\Omega_j\}_{j \in \mathcal{L}}, \mathcal{O}, \gamma \rangle \quad (1)$$

其中$\mathcal{L}$表示LEO卫星智能体集合。

**状态空间$\mathcal{S}$：** 状态空间$\mathcal{S}$定义了系统的全局状态，包含所有LEO卫星的物理状态（轨道位置$\mathbf{p}_j(t)$、剩余能量$e_j(t)$）、计算状态（CPU利用率$\rho_j^{cpu}(t)$、任务队列$\mathcal{Q}_j(t)$）以及星间链路质量矩阵$\mathbf{L}(t)$：

$$s(t) = \{\{\mathbf{p}_j(t), e_j(t), \rho_j^{cpu}(t), \mathcal{Q}_j(t)\}_{j \in \mathcal{L}}, \mathbf{L}(t)\} \quad (2)$$

**观测空间**:观测空间$\{\Omega_j\}_{j \in \mathcal{L}}$反映了LEO卫星的局部感知能力。由于通信和计算约束，智能体$j$只能获得局部观测：

$$o_j(t) = \{s_j^{local}(t), \mathcal{N}_j(t), \mathcal{T}_j^{user}(t)\} \quad (4)$$

其中$s_j^{local}(t)$是自身状态，$\mathcal{N}_j(t)$是可通信邻居状态，$\mathcal{T}_j^{user}(t)$是覆盖区域内的用户任务请求。

**动作空间**：OAAL框架采用分层决策架构，强化学习智能体专注于解决任务卸载问题。LEO智能体$j$的强化学习动作空间被简化为一个纯离散的卸载决策序列，其维度等于当前任务队列的长度$|\mathcal{Q}_j(t)|$：

$$\mathbf{D}_j(t) = (d_j^1, d_j^2, \ldots, d_j^{|\mathcal{Q}_j|})$$

其中，每个元素$d_j^k$的取值来自所有可能的任务分配目标集合$\mathcal{M}_j(t)$：本地处理、邻居卫星、地面站、任务丢弃。因此，智能体$j$在时刻$t$的强化学习动作为：

$$a_j^{RL}(t) = \mathbf{D}_j(t)$$

这种设计使得强化学习智能体能够专注于"任务应该在哪里处理"这一核心卸载决策问题，避免了传统方法中离散-连续混合动作空间带来的学习复杂性。

### 3.1.3 转移概率与观测函数

转移概率$\mathcal{P}$由轨道动力学、任务到达过程和系统动态共同决定。观测函数$\mathcal{O}$将全局状态映射到各智能体的局部观测，体现了分布式系统中信息的不完全性。

### 3.1.4 奖励函数设计

奖励函数$\{\mathcal{R}_j\}_{j \in \mathcal{L}}$设计为多目标优化，平衡任务完成率、能耗、延迟、负载均衡。本文采用分层奖励机制，包含个体奖励和全局奖励。

**个体奖励：** 考虑到任务的优先级差异，定义三个优先级等级：高优先级（Priority 1）、中优先级（Priority 2）、低优先级（Priority 3）。个体奖励函数为：

$$r_j^{local}(s, a) = \sum_{p=1}^{3} \alpha_p \cdot R_j^{comp,p} - \beta \cdot E_j^{cons} - \zeta \cdot D_j^{delay} \quad (5)$$

其中：$R_j^{comp,p}$表示智能体$j$完成的优先级$p$任务数量；$\alpha_p$表示优先级$p$任务的完成奖励权重，满足$\alpha_1 > \alpha_2 > \alpha_3$；$E_j^{cons}$表示能耗；$D_j^{delay}$表示平均延迟。

具体地，优先级权重设置为：$\alpha_1 = 10.0$，$\alpha_2 = 6.0$，$\alpha_3 = 3.0$。这种设计确保高优先级任务的完成能够获得更高的奖励，激励智能体优先处理重要任务。

**全局奖励：** 全局奖励函数为：

$$r_j^{regional}(s, a) = -\delta \cdot B_k(t) \quad (6)$$

其中$B_k(t)$表示区域$k$的负载方差，激励卫星做出有利于区域整体负载均衡的决策。

总奖励函数为：

$$r_j(s, a) = r_j^{local}(s, a) + r_j^{regional}(s, a) \quad (7)$$

其中$\alpha, \beta, \zeta, \delta$是权重参数，根据应用需求调节。

## 3.2 序列生成的MAPPO-Transformer卸载决策算法

### 3.2.1 生成式卸载策略设计

传统强化学习中的动作空间通常是固定维度的离散或连续空间，无法处理可变数量的任务队列。本文提出的生成式卸载策略从根本上重新定义了智能体的卸载决策机制。

**序列化卸载决策建模**：LEO智能体$j$的卸载动作空间被定义为一个可变长度的离散序列空间：

$$\mathcal{A}_j^{offload} = \bigcup_{n=1}^{N_{max}} \{local, neighbor_1, \ldots, neighbor_K, ground, drop\}^n \quad (12)$$

其中$N_{max}$是任务队列的最大容量，每个任务的卸载目标从本地处理、邻居卫星、地面站或丢弃中选择。一个具体的卸载动作$\mathbf{D}_j = (d_j^1, d_j^2, \ldots, d_j^{|\mathcal{Q}_j|})$是一个离散序列，序列长度等于当前任务队列长度$|\mathcal{Q}_j|$。

**基于Transformer的卸载策略生成器**：本文设计了一个端到端的序列生成模型，能够在单次前向传播中生成完整的任务卸载策略。该模型采用编码器-解码器架构：

$$\pi_{\theta_j}(\mathbf{D}_j|o_j) = \prod_{k=1}^{|\mathcal{Q}_j|} p_{\theta_j}(d_j^k | d_j^{<k}, o_j) \quad (13)$$

其中$d_j^{<k} = (d_j^1, \ldots, d_j^{k-1})$表示前$k-1$个卸载决策。

### 3.2.2 Transformer网络架构

**编码器设计**：编码器接收智能体的局部观测$o_j$和任务队列信息，生成上下文表示：

$$\mathbf{H}_{enc} = \text{Encoder}([\mathbf{e}_{state}; \mathbf{e}_{task}^1; \ldots; \mathbf{e}_{task}^{|\mathcal{Q}_j|}]) \quad (14)$$

其中$\mathbf{e}_{state} = \text{MLP}_{state}(o_j^{state})$是状态嵌入，$\mathbf{e}_{task}^k = \text{MLP}_{task}(task_k)$是第$k$个任务的嵌入。

**解码器设计**：解码器采用自回归方式生成卸载决策：

$$\mathbf{h}_k = \text{Decoder}(\mathbf{e}_{task}^k, \mathbf{H}_{enc}, \mathbf{h}_{<k}) \quad (15)$$

$$p_{\theta_j}(d_j^k | d_j^{<k}, o_j) = \text{Softmax}(\text{MLP}_{offload}(\mathbf{h}_k)) \quad (16)$$

**卸载约束掩码机制**：为确保生成的卸载策略满足物理约束，引入动态卸载掩码：

$$\text{mask}_j^k = \{target \in \{local, neighbor_i, ground, drop\} | \text{feasible}(task_k, target, state_j)\} \quad (17)$$

该掩码机制考虑通信链路状态、邻居卫星负载、任务类型限制等因素，确保卸载决策的可行性。

### 3.2.3 MAPPO训练算法

强化学习层采用多智能体近端策略优化（MAPPO）算法进行训练。MAPPO结合了集中式训练和分布式执行的优势，通过共享价值函数提升学习效率。

**策略更新**：采用PPO算法的截断代理目标函数，专门优化卸载决策：

$$\mathcal{L}_{RL}^{offload}(\theta_j) = \mathbb{E}_{(s,\mathbf{D},r) \sim \mathcal{D}_j} \left[ \min\left( \rho_t(\theta_j) \hat{A}_t^{offload}, \text{clip}(\rho_t(\theta_j), 1-\epsilon, 1+\epsilon) \hat{A}_t^{offload} \right) \right] \quad (18)$$

其中$\rho_t(\theta_j) = \frac{\pi_{\theta_j}(\mathbf{D}_t|s_t)}{\pi_{\theta_j^{old}}(\mathbf{D}_t|s_t)}$是重要性采样比率，$\hat{A}_t^{offload}$是卸载决策的优势函数估计。

**价值函数学习**：采用集中式价值函数，利用全局状态信息：

$$V_{\phi}(s_t) = \mathbb{E}\left[\sum_{k=0}^{\infty} \gamma^k r_{t+k} | s_t\right] \quad (19)$$

价值函数通过最小化均方误差进行更新：

$$\mathcal{L}_{V}(\phi) = \mathbb{E}[(V_{\phi}(s_t) - R_t)^2] \quad (20)$$

其中$R_t$是折扣累积奖励。

## 3.3 策略域：地理绑定的集体智慧机制

### 3.3.1 策略域划分与表示

策略域是OAAL框架的核心创新，它将地理空间特性与集体学习相结合，为分布式LEO智能体提供了区域性的先验知识指导。与传统的全局统一策略不同，策略域机制认识到不同地理区域具有独特的任务模式、用户行为和网络特性。

**地理空间划分**：采用基于经纬度的规则网格划分方法，将全球表面划分为$D = 24$个策略域。每个策略域$\mathcal{D}_d$覆盖经度范围$15°$，确保了区域内任务特性的相对一致性：

$$\mathcal{D}_d = \{(\text{lat}, \text{lon}) | \text{lon} \in [15d, 15(d+1)), \text{lat} \in [-90°, 90°]\} \quad (21)$$

其中$d \in \{0, 1, \ldots, 23\}$。这种划分方法考虑了地球自转特性，使得相同时区内的用户活动模式具有较强的相关性。

**域特征表示**：每个策略域$d$的特征向量$\mathbf{f}_d(t)$包含该区域的时空特性：

$$\mathbf{f}_d(t) = [\mathbf{f}_d^{geo}, \mathbf{f}_d^{temp}(t), \mathbf{f}_d^{task}(t), \mathbf{f}_d^{topo}(t)] \quad (22)$$

其中：$\mathbf{f}_d^{geo}$为静态地理特征（人口密度、经济发展水平、地形特征）；$\mathbf{f}_d^{temp}(t)$为时间特征（当地时间、季节、工作日/周末）；$\mathbf{f}_d^{task}(t)$为任务统计特征（任务到达率、类型分布、优先级分布）；$\mathbf{f}_d^{topo}(t)$为网络拓扑特征（卫星密度、链路质量、覆盖情况）。

**域策略网络架构**：每个策略域维护一个基于Transformer的策略网络$\pi_{\theta_d}$，其架构与LEO智能体的策略网络保持一致，以便进行有效的知识蒸馏：

$$\pi_{\theta_d}(a|o, \mathbf{f}_d) = \text{Transformer}_{\theta_d}(\text{Embed}(o) \oplus \text{Embed}(\mathbf{f}_d)) \quad (23)$$

其中$\oplus$表示特征拼接操作，$\text{Embed}(\cdot)$是嵌入函数。

### 3.3.2 双向学习机制

OAAL的核心创新在于LEO智能体与策略域之间的双向知识流动：

**下行知识传递**：当LEO智能体$j$进入策略域$d$时，通过知识蒸馏机制快速学习域策略：

$$\mathcal{L}_{distill} = \mathbb{E}_{o \sim \Omega_j} [D_{KL}(\pi_{\theta_d}(\cdot|o) \| \pi_{\theta_j}(\cdot|o))] \quad (24)$$

**上行经验贡献**：表现优异的LEO智能体离开域$d$时，其策略经验被融合到域策略中：

$$\theta_d^{new} = (1-\eta) \theta_d^{old} + \eta \sum_{j \in \mathcal{L}_d} w_j \theta_j \quad (25)$$

其中$w_j$是基于性能的权重，$\eta$是学习率，$\mathcal{L}_d$是近期访问域$d$的LEO集合。

### 3.3.3 集体智慧演进算法

**性能评估机制**：当LEO智能体$j$离开策略域$d$时，系统计算其在该域内的综合性能评分：

$$P_j^d = w_1 \cdot \frac{\text{completed\_tasks}_j^d}{\text{total\_tasks}_j^d} + w_2 \cdot (1 - \frac{\text{avg\_delay}_j^d}{\text{max\_delay}}) + w_3 \cdot (1 - \frac{\text{energy\_consumed}_j^d}{\text{energy\_budget}_j^d}) \quad (26)$$

其中$w_1, w_2, w_3$是权重参数，满足$w_1 + w_2 + w_3 = 1$。

**策略融合算法**：策略域$d$的更新采用基于性能加权的指数移动平均方法：

$$\theta_d^{t+1} = (1-\eta) \theta_d^t + \eta \sum_{j \in \mathcal{L}_d^{recent}} \frac{\exp(\beta P_j^d)}{\sum_{k \in \mathcal{L}_d^{recent}} \exp(\beta P_k^d)} \theta_j^{final} \quad (27)$$

其中$\eta \in (0, 1)$是域策略学习率，$\beta > 0$是温度参数，$\theta_j^{final}$是智能体$j$离开域$d$时的最终策略参数。

## 3.4 LEO智能体混合学习算法

### 3.4.1 混合学习目标函数

OAAL的核心创新在于设计了一个专门针对任务卸载决策的混合学习目标函数，它巧妙地平衡了智能体在卸载策略上的自主探索能力与对策略域集体智慧的学习。

**总体损失函数**：LEO智能体$j$的卸载学习目标由两部分组成：

$$\mathcal{L}_{total}^{offload}(\theta_j) = (1-\lambda) \mathcal{L}_{RL}^{offload}(\theta_j) + \lambda \mathcal{L}_{distill}^{offload}(\theta_j, \theta_{d(j)}) \quad (28)$$

其中$\lambda \in [0,1]$是平衡因子，$d(j)$表示智能体$j$当前所在的策略域。

**卸载知识蒸馏损失**：设计了专门针对卸载策略的序列级别知识蒸馏损失：

$$\mathcal{L}_{distill}^{offload}(\theta_j, \theta_d) = \mathbb{E}_{o \sim \Omega_j} \left[ D_{KL}(\pi_{\theta_d}^{offload}(\cdot|o, \mathbf{f}_d) \| \pi_{\theta_j}^{offload}(\cdot|o)) \right] \quad (29)$$

展开为卸载序列形式：

$$\mathcal{L}_{distill}^{offload} = \mathbb{E}_{o,\mathbf{D}} \left[ \sum_{k=1}^{|\mathcal{Q}_j|} D_{KL}(p_{\theta_d}(d^k|d^{<k}, o) \| p_{\theta_j}(d^k|d^{<k}, o)) \right] \quad (30)$$

### 3.4.2 自适应平衡机制

平衡因子$\lambda$采用自适应调节策略，根据智能体的学习进度和域策略的可信度动态调整：

$$\lambda(t) = \lambda_0 \cdot \exp\left(-\frac{(P_j^{current} - P_d^{avg})^2}{2\sigma^2}\right) \cdot \left(1 - \frac{t}{T_{total}}\right)^{\alpha} \quad (31)$$

其中$P_j^{current}$是智能体当前性能，$P_d^{avg}$是域内平均性能，$\sigma$控制性能差异的敏感度，$\alpha$控制时间衰减速度。

### 3.4.3 轨道感知的策略适应

LEO卫星的高动态特性要求智能体能够预测轨道变化并提前适应新的策略域。

**前瞻性策略加载**：当预测到智能体即将进入新的策略域$d'$时，系统提前加载该域的策略：

$$\pi_j^{transition} = \alpha(t) \pi_{\theta_j} + (1-\alpha(t)) \pi_{\theta_{d'}} \quad (32)$$

其中$\alpha(t)$是基于距离的插值权重：

$$\alpha(t) = \frac{\text{dist}(\mathbf{p}_j(t), \text{boundary}_{d \rightarrow d'})}{\text{transition\_zone\_width}} \quad (33)$$

**记忆增强机制**：智能体维护一个经验记忆库$\mathcal{M}_j$，存储在不同策略域的成功经验：

$$\mathcal{M}_j = \{(d_i, \theta_j^{d_i}, P_j^{d_i})\}_{i=1}^{N_{visited}} \quad (34)$$

当重新访问已知域时，智能体可以快速恢复之前的适应策略，实现快速重适应。

## 3.5 基于动态效用感知的自适应资源分配算法

### 3.5.1 DURA算法概述

DURA（Dynamic Utility-aware Resource Allocation）算法作为分层架构的第二层，专门负责处理本地资源分配问题。当强化学习智能体完成卸载决策后，DURA算法接管所有被标记为"本地处理"的任务，通过动态效用评估实现计算资源的自适应分配。

DURA算法将传统的串行任务选择问题转化为并行资源分配问题，实现了从"选择处理哪个任务"到"同时为多个任务分配资源"的范式转变。

### 3.5.2 动态效用函数设计

任务$T_i$在时刻$t_{now}$的动态效用函数$U(T_i, t_{now})$由三个加权分量构成：

$$U(T_i, t_{now}) = w_p \cdot f_p(P_i) + w_d \cdot f_d(D_i, t_{now}) + w_{fail} \cdot f_{fail}(W_i) \quad (8)$$

其中静态价值因子$f_p(P_i) = P_i$反映任务的固有重要性。动态紧迫性因子采用对数形式：

$$f_d(D_i, t_{now}) = \log\left(1 + \frac{T_{remain, max}}{D_i - t_{now} + \epsilon}\right) \quad (9)$$

该对数函数相比传统的倒数函数具有更好的数值稳定性，其增长曲线符合边际效用递减原理。$T_{remain, max}$作为归一化常数确保不同时间尺度下的可比性，$\epsilon$防止数值异常。失败惩罚因子$f_{fail}(W_i) = W_i$量化任务失败造成的损失，使效用函数的各分量具有统一的正向贡献特性。

### 3.5.3 基于效用的资源分配机制

卫星MEC在任意时刻$t$维护活跃任务集$Q_{active}$，总计算资源$F_{total}$根据各任务的动态效用值进行比例分配：

$$F_i(t) = F_{total} \cdot \frac{U(T_i, t)}{\sum_{j \in Q_{active}} U(T_j, t)} \quad (10)$$

该分配机制形成自适应反馈闭环：任务接近截止时间时，其效用值$U_i$增加，自动获得更多计算资源$F_i$，加速处理进程；非紧急任务则维持最小资源分配，避免占用关键资源。这种内生的资源调节机制无需外部干预即可实现动态优化。

### 3.5.4 两阶段队列与准入控制

系统采用两级队列架构：等待队列$Q_{wait}$和活跃处理队列$Q_{active}$。新到达任务首先进入$Q_{wait}$，调度器周期性评估其可行性。任务$T_i$的准入条件基于最差情况可行性检查：

$$t_{now} + \frac{C_i}{F_{min\_share}} \leq D_i \quad (11)$$

其中$C_i$为任务计算复杂度，$F_{min\_share}$为基于历史数据或理论界限确定的最小资源保证。满足可行性条件的任务进入$Q_{active}$参与资源分配，不满足条件的任务根据系统策略继续等待或直接拒绝。该准入控制机制从源头避免将资源浪费在注定无法完成的任务上，提高系统整体效率。

## 3.6 星间协同与故障恢复机制

### 3.6.1 图注意力网络的邻居协调

LEO卫星网络的拓扑结构随时间动态变化，传统的固定协同机制难以适应这种高动态性。本文提出基于图注意力网络（GAT）的自适应协同机制，能够根据网络拓扑和任务需求动态调整协同策略。

**动态邻居图构建**：在时刻$t$，系统为每个LEO智能体$j$构建其邻居图$\mathcal{G}_j(t) = (\mathcal{V}_j(t), \mathcal{E}_j(t))$，其中：

$$\mathcal{V}_j(t) = \{j\} \cup \{k \in \mathcal{L} | \text{dist}(\mathbf{p}_j(t), \mathbf{p}_k(t)) \leq R_{comm} \land \text{quality}(\text{link}_{j,k}(t)) \geq Q_{min}\} \quad (35)$$

$$\mathcal{E}_j(t) = \{(j,k) | k \in \mathcal{V}_j(t) \setminus \{j\}, \text{bandwidth}_{j,k}(t) \geq B_{min}\} \quad (36)$$

其中$R_{comm}$是通信半径，$Q_{min}$是最小链路质量阈值，$B_{min}$是最小带宽要求。

**多头注意力协同机制**：智能体$j$通过多头注意力机制聚合邻居信息：

$$\mathbf{h}_j^{(l+1)} = \text{Concat}_{h=1}^{H} \left( \sum_{k \in \mathcal{N}_j} \alpha_{jk}^{(h)} \mathbf{W}^{(h)} \mathbf{h}_k^{(l)} \right) \mathbf{W}^{out} \quad (37)$$

其中注意力权重$\alpha_{jk}^{(h)}$计算为：

$$\alpha_{jk}^{(h)} = \frac{\exp(\text{LeakyReLU}(\mathbf{a}^{(h)T} [\mathbf{W}^{(h)} \mathbf{h}_j^{(l)} \| \mathbf{W}^{(h)} \mathbf{h}_k^{(l)} \| \mathbf{e}_{jk}]))}{\sum_{m \in \mathcal{N}_j} \exp(\text{LeakyReLU}(\mathbf{a}^{(h)T} [\mathbf{W}^{(h)} \mathbf{h}_j^{(l)} \| \mathbf{W}^{(h)} \mathbf{h}_m^{(l)} \| \mathbf{e}_{jm}]))} \quad (38)$$

其中$\mathbf{e}_{jk}$是边特征，包含链路质量、延迟、带宽等信息。

### 3.6.2 分布式任务协调算法

基于图注意力网络的输出，智能体间进行分布式任务协调。当智能体$j$的任务队列过载时，它通过以下机制寻求邻居协助：

**负载评估**：计算自身负载压力$\rho_j$：

$$\rho_j = \frac{|\mathcal{Q}_j| \cdot \bar{w}_j}{\text{capacity}_j} \quad (39)$$

其中$\bar{w}_j$是任务平均权重，$\text{capacity}_j$是处理能力。

**协助请求生成**：当$\rho_j > \rho_{threshold}$时，生成协助请求：

$$\text{request}_j = \{\text{task\_subset}, \text{urgency}, \text{reward\_offer}\} \quad (40)$$

**邻居响应决策**：邻居$k$基于自身状态和协同收益决定是否接受：

$$\text{accept}_{k \leftarrow j} = \mathbb{I}[\rho_k < \rho_{safe} \land \text{reward\_offer} > \text{cost}_{transfer}] \quad (41)$$

### 3.6.3 系统鲁棒性与故障恢复

OAAL框架设计了多层次的故障检测与恢复机制，确保系统在面临各种故障时仍能维持优雅的性能降级。

**故障模式识别**：基于历史故障数据，策略域学习常见故障模式：

$$P(\text{failure\_type} | \text{symptoms}) = \text{Classifier}_d(\text{symptoms}) \quad (42)$$

**动态重路由**：故障发生时，智能体动态调整任务分配策略：

$$\pi_j^{recovery}(a|o) = \text{mask}(\pi_j(a|o), \text{failed\_targets}) \quad (43)$$

**性能优雅降级机制**：当系统资源不足时，采用基于任务优先级的优雅降级：

$$\text{priority\_score}(task_i) = w_1 \cdot \text{urgency}_i + w_2 \cdot \text{value}_i + w_3 \cdot \text{user\_tier}_i \quad (44)$$

## 3.7 分层协同调度流程

整个系统的调度流程体现了强化学习卸载决策与DURA资源分配的紧密协同，具体流程如下：

**步骤1：任务到达与队列管理**

新任务到达时被赋予参数$(P_i, D_i, W_i, C_i)$并加入任务队列$\mathcal{Q}_j$。

**步骤2：强化学习卸载决策**

LEO智能体基于当前观测生成卸载策略$\mathbf{D}_j$，为每个任务指定处理位置（本地、邻居、地面站或丢弃）。

**步骤3：本地任务筛选**

根据卸载决策，提取所有标记为"本地处理"的任务，形成本地任务集$\mathcal{Q}_j^{local}$。

**步骤4：DURA资源分配**

对于本地任务集，DURA算法执行以下步骤：

(1) 将本地任务分为等待队列$Q_{wait}$和活跃队列$Q_{active}$；

(2) 基于可行性检查将合格任务从$Q_{wait}$移入$Q_{active}$；

(3) 实时计算活跃任务的动态效用值并据此分配计算资源；

(4) 任务完成后释放资源，在下一周期重新分配。

**步骤5：反馈与学习**

DURA的资源分配结果作为环境反馈影响强化学习的奖励函数，形成闭环优化。

该分层协同机制确保了卸载决策与资源分配的最优匹配，实现了系统整体性能的最大化。

## 3.8 系统整体学习目标与优化

### 3.8.1 分层优化目标

整个系统的学习目标可以表述为最大化卸载决策的长期累积奖励：

$$\max_{\{\pi_{\theta_j}^{offload}\}_{j \in \mathcal{L}}} \mathbb{E}\left[\sum_{t=0}^{\infty} \gamma^t \sum_{j \in \mathcal{L}} r_j^{total}(s_t, \mathbf{D}_t)\right] \quad (45)$$

其中$\gamma \in [0,1)$是折扣因子，$\mathbf{D}_t$是时刻$t$的卸载决策向量。

系统采用分层优化策略，强化学习层专注于卸载决策优化，DURA层专注于资源分配优化：

**上层目标**：$\max_{\{\pi_{\theta_j}^{offload}\}} \mathbb{E}[R^{offload}]$ （强化学习优化卸载策略）

**下层目标**：$\max_{F_k} \sum_{k \in \mathcal{Q}^{local}} U(T_k, t) \cdot \mathbb{I}[\text{completed}(T_k)]$ （DURA优化资源分配）

### 3.8.2 强化学习与DURA算法的协同机制

OAAL框架通过精心设计的分层协同机制实现强化学习卸载决策与DURA资源分配的无缝集成：

**决策时序协调**：在每个决策时刻$t$，系统按以下顺序执行：

(1) 强化学习智能体基于当前观测$o_j(t)$生成卸载策略$\mathbf{D}_j(t)$；

(2) 根据卸载决策确定本地任务集$\mathcal{Q}_j^{local}(t)$；

(3) DURA算法对本地任务集进行实时资源分配。

**信息反馈机制**：DURA算法的资源分配结果作为环境反馈影响强化学习的奖励函数：

$$r_j^{local}(t) = \sum_{k \in \mathcal{Q}_j^{local}(t)} \alpha_k \cdot \mathbb{I}[\text{completed}(task_k)] - \beta \cdot \sum_{k \in \mathcal{Q}_j^{local}(t)} F_k(t) \quad (46)$$

其中第一项奖励成功完成的本地任务，第二项惩罚过度的资源消耗。

**负载感知的卸载决策**：强化学习智能体的观测空间包含DURA算法提供的本地负载信息：

$$o_j^{load}(t) = \{\text{utilization}_j(t), \text{queue\_length}_j(t), \text{avg\_utility}_j(t)\} \quad (47)$$

这使得卸载决策能够感知本地资源分配状态，实现更智能的负载均衡。

### 3.8.3 系统收敛性与性能保证

通过策略域的引导和智能体间的协作，OAAL能够在保持分布式决策优势的同时，实现卸载决策与资源分配的协同优化。系统具有以下理论保证：

**收敛性保证**：在满足标准强化学习收敛条件下，OAAL框架中的LEO智能体策略收敛到局部最优。

**性能上界**：对于$f$比例的节点故障，OAAL的性能保留率理论上界为：

$$P_{retain}^{OAAL} = (1-f) \times \eta_{cooperation} \quad (48)$$

其中$\eta_{cooperation} > 1$是协同增益因子。

**适应性保证**：系统自适应时间满足：

$$T_{adapt}^{OAAL} < 0.5 \times T_{adapt}^{baseline} \quad (49)$$

通过上述完整的算法设计，OAAL框架实现了真正的智能化任务调度：既能够从策略域的集体智慧中快速学习，又保持了独立探索和创新的能力；既能够适应当前环境的特定需求，又具备了预测和适应未来变化的前瞻性。

